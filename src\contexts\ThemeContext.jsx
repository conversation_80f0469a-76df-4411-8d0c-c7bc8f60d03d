import { createContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export { ThemeContext };

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    // Recuperar tema del localStorage o usar 'dark' por defecto
    const savedTheme = localStorage.getItem('loginTheme');
    return savedTheme || 'dark';
  });

  // Función para cambiar tema
  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'day' : 'dark';
    setTheme(newTheme);
    // Guardar en localStorage
    localStorage.setItem('loginTheme', newTheme);
  };

  // Función para establecer tema específico
  const setThemeExplicit = (newTheme) => {
    setTheme(newTheme);
    localStorage.setItem('loginTheme', newTheme);
  };

  // Sincronizar con cambios en localStorage (por si se cambia desde otra pestaña)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'loginTheme') {
        setTheme(e.newValue || 'dark');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const value = {
    theme,
    toggleTheme,
    setTheme: setThemeExplicit,
    isDark: theme === 'dark',
    isLight: theme === 'day'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
