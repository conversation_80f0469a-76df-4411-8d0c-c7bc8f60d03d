import { useAuth } from '../hooks/useAuth';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { useTheme } from '../hooks/useTheme';
import { BorderBeam } from "@/components/magicui/border-beam";

const Dashboard = () => {
  const { user } = useAuth();
  const { theme } = useTheme();
  
  const containerRef = useRef(null);
  const headerRef = useRef(null);
  const cardRef = useRef(null);

  useEffect(() => {
    const tl = gsap.timeline();

    // Header con efecto de aparición más suave
    tl.fromTo(headerRef.current,
      { y: -50, opacity: 0, scale: 0.95 },
      { y: 0, opacity: 1, scale: 1, duration: 1.2, ease: "power3.out" }
    );

    // Card central con efecto de aparición más suave y elegante
    tl.fromTo(cardRef.current,
      { y: 80, opacity: 0, scale: 0.9, rotationX: 5 },
      { y: 0, opacity: 1, scale: 1, rotationX: 0, duration: 1.4, ease: "power2.out" },
      "-=0.8"
    );

    // Agregar un pequeño delay inicial para que se sienta más natural
    tl.delay(0.3);
  }, []);

  return (
    <div ref={containerRef} className="w-full max-w-4xl mx-auto h-full flex flex-col px-3 sm:px-4 lg:px-6">

      <div className="relative z-10 flex-1">
        {/* Header del panel - Más grande y prominente */}
        <div ref={headerRef} className="mb-4 sm:mb-6 text-center">
          <h1 className={`text-xl sm:text-2xl lg:text-3xl font-bold mb-2 transition-all duration-700 ${
            theme === 'dark' ? 'text-white' : 'text-gray-800'
          }`}>
            Panel de Usuario
          </h1>
          <p className={`text-sm sm:text-base lg:text-lg transition-all duration-700 ${
            theme === 'dark' ? 'text-gray-200' : 'text-gray-600'
          }`}>
            Bienvenido al sistema QRNG Raffle
          </p>
        </div>

        {/* Card central principal con BorderBeam */}
        <div ref={cardRef} className={`relative backdrop-blur-xl shadow-2xl border p-4 sm:p-6 lg:p-8 flex-1 rounded-xl sm:rounded-2xl transition-all duration-700 ${
          theme === 'dark' 
            ? 'bg-black/30 shadow-purple-500/20 border-purple-400/30' 
            : 'bg-white/30 shadow-blue-500/20 border-blue-400/30'
        }`}>
          <BorderBeam duration={8} size={100} />
          <div className="space-y-4 sm:space-y-6 h-full">
            {/* Información del usuario - Más grande y espacioso */}
            <div>
              <h2 className={`text-base sm:text-lg font-semibold mb-2 sm:mb-3 transition-all duration-500 ${
                theme === 'dark' ? 'text-white' : 'text-gray-800'
              }`}>
                Información de su cuenta
              </h2>
              
              <div className={`overflow-hidden rounded-lg border transition-all duration-500 hover:scale-[1.01] ${
                theme === 'dark' 
                  ? 'bg-black/40 border-purple-400/30' 
                  : 'bg-white/40 border-blue-400/30'
              }`}>
                <table className="min-w-full divide-y divide-slate-600/50">
                  <thead className={theme === 'dark' ? 'bg-black/60' : 'bg-gray-200/60'}>
                    <tr>
                      <th className={`px-3 sm:px-4 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium uppercase tracking-wider transition-colors duration-300 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Tipo de usuario
                      </th>
                      <th className={`px-3 sm:px-4 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium uppercase tracking-wider transition-colors duration-300 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Nombre
                      </th>
                      <th className={`px-3 sm:px-4 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium uppercase tracking-wider transition-colors duration-300 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Correo
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`divide-y transition-all duration-700 ${
                    theme === 'dark' 
                      ? 'bg-black/30 divide-purple-400/20' 
                      : 'bg-white/30 divide-blue-400/20'
                  }`}>
                    <tr className={`transition-all duration-300 hover:scale-[1.01] ${
                      theme === 'dark' ? 'hover:bg-black/50' : 'hover:bg-white/50'
                    }`}>
                      <td className={`px-3 sm:px-4 py-2 sm:py-3 whitespace-nowrap text-sm sm:text-base font-medium transition-colors duration-300 ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>
                        {user?.role === 'admin' ? 'Administrador' : 'Usuario'}
                      </td>
                      <td className={`px-3 sm:px-4 py-2 sm:py-3 whitespace-nowrap text-sm sm:text-base font-medium transition-colors duration-300 ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>
                        {user?.name || user?.email?.split('@')[0] || 'Admin1'}
                      </td>
                      <td className={`px-3 sm:px-4 py-2 sm:py-3 whitespace-nowrap text-sm sm:text-base font-medium transition-colors duration-300 ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>
                        {user?.email || '<EMAIL>'}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Demos accesibles - Más grande y espacioso */}
            <div>
              <h2 className={`text-base sm:text-lg font-semibold mb-2 sm:mb-3 transition-all duration-500 ${
                theme === 'dark' ? 'text-white' : 'text-gray-800'
              }`}>
                Demos disponibles
              </h2>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                <div className={`rounded-lg border p-3 sm:p-4 lg:p-6 hover:scale-[1.02] transition-all duration-500 ease-out shadow-lg hover:shadow-xl ${
                  theme === 'dark' 
                    ? 'bg-black/40 border-purple-400/30 hover:bg-black/50 hover:border-purple-400/50' 
                    : 'bg-white/40 border-blue-400/30 hover:bg-white/50 hover:border-blue-400/50'
                }`}>
                  <h3 className={`text-base sm:text-lg font-medium mb-2 transition-all duration-300 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    🔐 QRNG Demo
                  </h3>
                  <p className={`text-xs sm:text-sm lg:text-base transition-all duration-300 mb-2 sm:mb-3 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                  }`}>
                    Generador de números aleatorios cuánticos
                  </p>
                  <a href="#" className="inline-block text-blue-400 hover:text-blue-300 transition-all duration-300 hover:translate-x-1 text-xs sm:text-sm font-medium">
                    Acceder →
                  </a>
                </div>
                
                <div className={`rounded-lg border p-3 sm:p-4 lg:p-6 hover:scale-[1.02] transition-all duration-500 ease-out shadow-lg hover:shadow-xl ${
                  theme === 'dark' 
                    ? 'bg-black/40 border-purple-400/30 hover:bg-black/50 hover:border-purple-400/50' 
                    : 'bg-white/40 border-blue-400/30 hover:bg-white/50 hover:border-blue-400/50'
                }`}>
                  <h3 className={`text-base sm:text-lg font-medium mb-2 transition-all duration-300 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    🎰 Quantum Raffle Marina del Sol
                  </h3>
                  <p className={`text-xs sm:text-sm lg:text-base transition-all duration-300 mb-2 sm:mb-3 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                  }`}>
                    Sorteo cuántico para Marina del Sol
                  </p>
                  <a href="#" className="inline-block text-blue-400 hover:text-blue-300 transition-all duration-300 hover:translate-x-1 text-xs sm:text-sm font-medium">
                    Acceder →
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
