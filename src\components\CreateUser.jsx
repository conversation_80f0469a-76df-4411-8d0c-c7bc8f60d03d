import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { gsap } from 'gsap';

const CreateUser = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { token, user: currentUser } = useAuth();
  
  // Refs para GSAP
  const containerRef = useRef(null);
  const headerRef = useRef(null);
  const formRef = useRef(null);
  const cardRef = useRef(null);
  
  const isEditMode = Boolean(id) && id !== 'create';
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Animaciones GSAP
  useEffect(() => {
    const tl = gsap.timeline();
    
    // Header con efecto de aparición elegante
    tl.fromTo(headerRef.current,
      { y: -30, opacity: 0, scale: 0.95 },
      { y: 0, opacity: 1, scale: 1, duration: 1.0, ease: "power3.out" }
    );
    
    // Card principal con efecto de aparición suave
    tl.fromTo(cardRef.current,
      { y: 50, opacity: 0, scale: 0.9 },
      { y: 0, opacity: 1, scale: 1, duration: 1.2, ease: "power2.out" },
      "-=0.6"
    );
    
    // Formulario con efecto stagger
    tl.fromTo(formRef.current.children,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, ease: "power2.out" },
      "-=0.8"
    );
    
    tl.delay(0.2);
  }, []);

  const loadUserData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:3000/users/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Usuario no encontrado');
        }
        throw new Error('Error al cargar datos del usuario');
      }

      const userData = await response.json();
      setFormData({
        name: userData.name || '',
        email: userData.email || '',
        password: '',
        confirmPassword: '',
        role: userData.role || 'user'
      });
    } catch (error) {
      setError('Error al cargar datos del usuario: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [id, token]);

  useEffect(() => {
    if (isEditMode && id) {
      loadUserData();
    }
  }, [id, isEditMode, loadUserData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!isEditMode) {
      if (!formData.password || formData.password.length < 8) {
        setError('La contraseña debe tener al menos 8 caracteres');
        return false;
      }
      if (formData.password !== formData.confirmPassword) {
        setError('Las contraseñas no coinciden');
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      if (isEditMode) {
        const response = await fetch(`http://localhost:3000/users/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            email: formData.email,
            role: formData.role
          })
        });

        if (!response.ok) {
          throw new Error('Error al actualizar usuario');
        }

        setSuccess('Usuario actualizado correctamente');
        setTimeout(() => {
          navigate('/users');
        }, 1500);
      } else {
        const response = await fetch('http://localhost:3000/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            name: formData.name,
            email: formData.email,
            password: formData.password,
            role: formData.role
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Error al crear usuario');
        }

        setSuccess('Usuario creado correctamente');
        setFormData({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          role: 'user'
        });
        
        setTimeout(() => {
          navigate('/users');
        }, 1500);
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const getTitle = () => isEditMode ? 'Editar Usuario' : 'Crear Nuevo Usuario';
  const getSubtitle = () => isEditMode 
    ? 'Modifica la información del usuario seleccionado' 
    : 'Completa el formulario para crear un nuevo usuario en el sistema';
  const getButtonText = () => loading 
    ? (isEditMode ? 'Actualizando...' : 'Creando...') 
    : (isEditMode ? 'Actualizar Usuario' : 'Crear Usuario');

  if (isEditMode) {
    return (
      <div ref={containerRef} className="w-full max-w-4xl mx-auto px-4 sm:px-0 pb-12">
        {/* Header elegante */}
        <div ref={headerRef} className="mb-6 text-center">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
            {getTitle()}
          </h1>
          <p className="text-base text-slate-300 mt-2 max-w-2xl mx-auto">
            {getSubtitle()}
          </p>
        </div>

        {/* Card principal elegante con scroll */}
        <div ref={cardRef} className="bg-slate-800/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-700/50 overflow-hidden max-h-[65vh] flex flex-col">
          {/* Header de la card - fijo */}
          <div className="bg-gradient-to-r from-slate-700/50 to-slate-600/50 px-6 py-4 border-b border-slate-600/50 flex-shrink-0">
            <h2 className="text-lg font-semibold text-white">Información del Usuario</h2>
            <p className="text-slate-300 text-sm mt-1">Datos actuales del usuario</p>
          </div>
          
          {/* Contenido del formulario con scroll */}
          <div className="p-6 overflow-y-auto flex-1 scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-800/50 hover:scrollbar-thumb-slate-500">
            <form ref={formRef} onSubmit={handleSubmit} className="space-y-5 pb-6">
              {/* Nombre (solo lectura) */}
              <div className="group">
                <label htmlFor="name" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                  Nombre Completo
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    disabled
                    className="w-full px-4 py-3 bg-slate-600/50 border border-slate-500/50 rounded-xl text-slate-300 cursor-not-allowed transition-all duration-300 group-hover:border-slate-500/50"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                </div>
                <p className="text-xs text-slate-400 mt-1 flex items-center">
                  <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  El nombre no se puede cambiar
                </p>
              </div>

              {/* Email */}
              <div className="group">
                <label htmlFor="email" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                  Correo Electrónico
                </label>
                <div className="relative">
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white placeholder-slate-400 transition-all duration-300 group-hover:border-slate-500/50"
                    placeholder="<EMAIL>"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Rol - Solo mostrar si es admin */}
              {currentUser?.role === 'admin' && (
                <div className="group">
                  <label htmlFor="role" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                    Rol del Usuario
                  </label>
                  <div className="relative">
                    <select
                      id="role"
                      name="role"
                      value={formData.role}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white transition-all duration-300 group-hover:border-slate-500/50 appearance-none"
                    >
                      <option value="user">Usuario</option>
                      <option value="admin">Administrador</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              )}

              {/* Mensajes de error y éxito */}
              {error && (
                <div className="bg-red-900/30 border border-red-700/50 text-red-200 px-4 py-3 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center">
                    <svg className="h-4 w-4 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {error}
                  </div>
                </div>
              )}

              {success && (
                <div className="bg-green-900/30 border border-green-700/50 text-green-200 px-4 py-3 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center">
                    <svg className="h-4 w-4 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {success}
                  </div>
                </div>
              )}

              {/* Botones */}
              <div className="flex space-x-4 pt-4">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl border border-blue-500/50"
                >
                  {getButtonText()}
                </button>
                
                <button
                  type="button"
                  onClick={() => navigate('/users')}
                  className="flex-1 bg-slate-700/50 text-white py-3 px-6 rounded-xl hover:bg-slate-600/50 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 border border-slate-600/50"
                >
                  Cancelar
                </button>
              </div>

              {/* Botón para cambiar contraseña */}
              <div className="flex justify-center pt-2">
                <button
                  type="button"
                  onClick={() => navigate(`/users/${id}/change-password`)}
                  className="text-blue-400 hover:text-blue-300 underline text-sm transition-colors duration-300 flex items-center"
                >
                  <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                  Cambiar Contraseña de este Usuario
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  // Modo creación - formulario completo
  return (
    <div ref={containerRef} className="w-full max-w-4xl mx-auto px-4 sm:px-0 pb-12">
      {/* Header elegante */}
      <div ref={headerRef} className="mb-6 text-center">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
          {getTitle()}
        </h1>
        <p className="text-base text-slate-300 mt-2 max-w-2xl mx-auto">
          {getSubtitle()}
        </p>
      </div>

      {/* Card principal elegante con scroll */}
      <div ref={cardRef} className="bg-slate-800/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-700/50 overflow-hidden max-h-[65vh] flex flex-col">
        {/* Header de la card - fijo */}
        <div className="bg-gradient-to-r from-slate-700/50 to-slate-600/50 px-6 py-4 border-b border-slate-600/50 flex-shrink-0">
          <h2 className="text-lg font-semibold text-white">Información del Nuevo Usuario</h2>
          <p className="text-slate-300 text-sm mt-1">Completa todos los campos requeridos</p>
        </div>
        
        {/* Contenido del formulario con scroll mejorado */}
        <div className="p-6 overflow-y-auto flex-1 scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-800/50 hover:scrollbar-thumb-slate-500">
          <form ref={formRef} onSubmit={handleSubmit} className="space-y-5 pb-6">
            {/* Nombre */}
            <div className="group">
              <label htmlFor="name" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                Nombre Completo <span className="text-red-400">*</span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white placeholder-slate-400 transition-all duration-300 group-hover:border-slate-500/50"
                  placeholder="Ingresa el nombre completo"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Email */}
            <div className="group">
              <label htmlFor="email" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                Correo Electrónico <span className="text-red-400">*</span>
              </label>
              <div className="relative">
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white placeholder-slate-400 transition-all duration-300 group-hover:border-slate-500/50"
                  placeholder="<EMAIL>"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Contraseña */}
            <div className="group">
              <label htmlFor="password" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                Contraseña <span className="text-red-400">*</span>
              </label>
              <div className="relative">
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white placeholder-slate-400 transition-all duration-300 group-hover:border-slate-500/50"
                  placeholder="Mínimo 8 caracteres"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
              </div>
              <p className="text-xs text-slate-400 mt-1">La contraseña debe tener al menos 8 caracteres</p>
            </div>

            {/* Confirmar Contraseña */}
            <div className="group">
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                Confirmar Contraseña <span className="text-red-400">*</span>
              </label>
              <div className="relative">
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white placeholder-slate-400 transition-all duration-300 group-hover:border-slate-500/50"
                  placeholder="Repite la contraseña"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Rol - Solo mostrar si es admin */}
            {currentUser?.role === 'admin' && (
              <div className="group">
                <label htmlFor="role" className="block text-sm font-medium text-slate-300 mb-2 group-hover:text-blue-300 transition-colors">
                  Rol del Usuario
                </label>
                <div className="relative">
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white transition-all duration-300 group-hover:border-slate-500/50 appearance-none"
                  >
                    <option value="user">Usuario</option>
                    <option value="admin">Administrador</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            )}

            {/* Mensajes de error y éxito */}
            {error && (
              <div className="bg-red-900/30 border border-red-700/50 text-red-200 px-4 py-3 rounded-xl backdrop-blur-sm">
                <div className="flex items-center">
                  <svg className="h-4 w-4 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {error}
                </div>
              </div>
            )}

            {success && (
              <div className="bg-green-900/30 border border-green-700/50 text-green-200 px-4 py-3 rounded-xl backdrop-blur-sm">
                <div className="flex items-center">
                  <svg className="h-4 w-4 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {success}
                </div>
              </div>
            )}

            {/* Botones */}
            <div className="flex space-x-4 pt-4">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl border border-blue-500/50"
              >
                {getButtonText()}
              </button>
              
              <button
                type="button"
                onClick={() => navigate('/users')}
                className="flex-1 bg-slate-700/50 text-white py-3 px-6 rounded-xl hover:bg-slate-600/50 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 border border-slate-600/50"
              >
                Cancelar
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateUser;
