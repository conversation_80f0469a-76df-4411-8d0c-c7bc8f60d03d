import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { BorderBeam } from "@/components/magicui/border-beam";
import { ShimmerButton } from "@/components/magicui/shimmer-button";
import { useTheme } from '../hooks/useTheme';

const Profile = () => {
  const navigate = useNavigate();
  const { token } = useAuth();
  const { theme } = useTheme();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: ''
  });
  const [loading, setLoading] = useState(false);
  const [profileLoading, setProfileLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [profileData, setProfileData] = useState(null);

  // Cargar datos del perfil desde el backend
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setProfileLoading(true);
        const response = await fetch('http://localhost:3000/users/profile', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('Error al cargar el perfil');
        }

        const profile = await response.json();
        setProfileData(profile);
        setFormData({
          name: profile.name || '',
          email: profile.email || '',
          role: profile.role || ''
        });
      } catch (error) {
        setError('Error al cargar el perfil: ' + error.message);
      } finally {
        setProfileLoading(false);
      }
    };

    if (token) {
      fetchProfile();
    }
  }, [token]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // ACTUALIZAR PERFIL PROPIO - PUT /users/profile
      const response = await fetch(`http://localhost:3000/users/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          email: formData.email
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 409) {
          throw new Error('El email ya existe en el sistema');
        } else if (response.status === 400) {
          throw new Error(errorData.message || 'Datos inválidos');
        } else {
          throw new Error(errorData.message || 'Error al actualizar perfil');
        }
      }

      setSuccess('Perfil actualizado correctamente');
      
      // Recargar datos del perfil para reflejar los cambios
      const updatedResponse = await fetch('http://localhost:3000/users/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (updatedResponse.ok) {
        const updatedProfile = await updatedResponse.json();
        setProfileData(updatedProfile);
        setFormData({
          name: updatedProfile.name || '',
          email: updatedProfile.email || '',
          role: updatedProfile.role || ''
        });
      }
      
      // Redirigir después de 2 segundos
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);

    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const getRoleDisplay = (role) => {
    const roleConfig = {
      admin: { 
        color: theme === 'dark' 
          ? 'bg-red-900/50 text-red-200 border-red-700/50' 
          : 'bg-red-100 text-red-800 border-red-300',
        text: 'Administrador' 
      },
      user: { 
        color: theme === 'dark'
          ? 'bg-blue-900/50 text-blue-200 border-blue-700/50'
          : 'bg-blue-100 text-blue-800 border-blue-300',
        text: 'Usuario' 
      }
    };
    
    const config = roleConfig[role] || roleConfig.user;
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (profileLoading || !profileData) {
    return (
      <div className="relative min-h-screen">
        <div className="max-w-2xl mx-auto relative z-10">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-400 mx-auto"></div>
            <p className="mt-4 text-slate-300">Cargando perfil...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen">
      <div className="max-w-2xl mx-auto relative z-10 px-3 sm:px-4 lg:px-6">
        {/* Header */}
        <div className="mb-6 sm:mb-8 text-center">
          <h1 className={`text-3xl sm:text-4xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Mi Perfil</h1>
          <p className={`text-base sm:text-lg ${theme === 'dark' ? 'text-gray-200' : 'text-gray-600'} mt-2`}>
            Gestiona tu información personal
          </p>
        </div>

        {/* Formulario con BorderBeam */}
        <div className={`relative backdrop-blur-xl shadow-2xl border p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl ${
          theme === 'dark' 
            ? 'bg-black/30 shadow-purple-500/20 border-purple-400/30' 
            : 'bg-white/30 shadow-blue-500/20 border-blue-400/30'
        }`}>
          <BorderBeam duration={8} size={100} />
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Información del usuario */}
            <div className={`mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg border ${
              theme === 'dark' 
                ? 'bg-black/40 border-purple-400/30' 
                : 'bg-white/40 border-blue-400/30'
            }`}>
              <div className="flex items-center space-x-3 sm:space-x-4">
                <div className={`h-12 w-12 sm:h-16 sm:w-16 rounded-full flex items-center justify-center ${
                  theme === 'dark' ? 'bg-gray-600' : 'bg-gray-400'
                }`}>
                  <span className={`text-lg sm:text-2xl font-bold ${
                    theme === 'dark' ? 'text-white' : 'text-gray-800'
                  }`}>
                    {profileData.name?.charAt(0) || profileData.email?.charAt(0) || 'U'}
                  </span>
                </div>
                <div>
                  <h3 className={`text-base sm:text-lg font-medium ${
                    theme === 'dark' ? 'text-white' : 'text-gray-800'
                  }`}>{profileData.name || 'Sin nombre'}</h3>
                  <p className={`text-sm sm:text-base ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>{profileData.email}</p>
                  <div className="mt-1 sm:mt-2">
                    {getRoleDisplay(profileData.role)}
                  </div>
                </div>
              </div>
            </div>

            {/* Nombre */}
            <div>
              <label htmlFor="name" className={`block text-sm sm:text-base font-medium mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-800'
              }`}>
                Nombre Completo
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className={`w-full px-3 sm:px-4 py-2 sm:py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 h-10 sm:h-12 text-sm sm:text-base ${
                  theme === 'dark'
                    ? 'bg-black/40 border-purple-400/50 focus:border-blue-400/70 focus:ring-blue-400/50 text-white placeholder:text-gray-300'
                    : 'bg-white/40 border-blue-400/50 focus:border-purple-400/70 focus:ring-purple-400/50 text-gray-800 placeholder:text-gray-500'
                }`}
                placeholder="Ingrese el nombre completo"
              />
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className={`block text-sm sm:text-base font-medium mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-800'
              }`}>
                Correo Electrónico
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className={`w-full px-3 sm:px-4 py-2 sm:py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 h-10 sm:h-12 text-sm sm:text-base ${
                  theme === 'dark'
                    ? 'bg-black/40 border-purple-400/50 focus:border-blue-400/70 focus:ring-blue-400/50 text-white placeholder:text-gray-300'
                    : 'bg-white/40 border-blue-400/50 focus:border-purple-400/70 focus:ring-purple-400/50 text-gray-800 placeholder:text-gray-500'
                }`}
                placeholder="<EMAIL>"
              />
            </div>

            {/* Mensajes de error y éxito */}
            {error && (
              <div className={`border px-3 sm:px-4 py-2 sm:py-3 rounded-md ${
                theme === 'dark'
                  ? 'bg-red-900/50 border-red-700/50 text-red-200'
                  : 'bg-red-100 border-red-300 text-red-800'
              }`}>
                {error}
              </div>
            )}

            {success && (
              <div className={`border px-3 sm:px-4 py-2 sm:py-3 rounded-md ${
                theme === 'dark'
                  ? 'bg-green-900/50 border-green-700/50 text-green-200'
                  : 'bg-green-100 border-green-300 text-green-800'
              }`}>
                {success}
              </div>
            )}

            {/* Botones con ShimmerButton */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 pt-4">
              <ShimmerButton 
                onClick={handleSubmit}
                disabled={loading}
                className="flex-1 shadow-2xl"
              >
                <span className="whitespace-pre-wrap text-center text-sm sm:text-base font-medium leading-none tracking-tight text-white">
                  {loading ? 'Actualizando...' : 'Actualizar Perfil'}
                </span>
              </ShimmerButton>
              
              <ShimmerButton 
                onClick={() => navigate('/change-password')}
                className="flex-1 shadow-2xl"
              >
                <span className="whitespace-pre-wrap text-center text-sm sm:text-base font-medium leading-none tracking-tight text-white">
                  Cambiar Contraseña
                </span>
              </ShimmerButton>
            </div>

            <div className="flex justify-center pt-2">
              <button
                type="button"
                onClick={() => navigate('/dashboard')}
                className={`underline text-sm sm:text-base ${
                  theme === 'dark' ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-600'
                }`}
              >
                Volver al Dashboard
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Profile;
