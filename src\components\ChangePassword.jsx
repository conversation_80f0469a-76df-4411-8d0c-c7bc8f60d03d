import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { BorderBeam } from "@/components/magicui/border-beam";
import { ShimmerButton } from "@/components/magicui/shimmer-button";
import { useTheme } from '../hooks/useTheme';

const ChangePassword = () => {
  const navigate = useNavigate();
  const { id } = useParams(); // Para obtener el ID del usuario
  const { token, user: currentUser } = useAuth();
  const { theme } = useTheme();
  
  // Detectar si estamos cambiando la contraseña de otro usuario o la propia
  const isOwnPassword = !id || currentUser?.id === parseInt(id);
  // const targetUserId = id || currentUser?.id; // Variable no utilizada actualmente
  
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmNewPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, feedback: [] });

  // Validación de contraseña según estándares del backend
  const validatePasswordStrength = (password) => {
    const feedback = [];
    let score = 0;

    if (password.length >= 8) score += 1;
    else feedback.push('Mínimo 8 caracteres');

    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('Al menos una letra minúscula (a-z)');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('Al menos una letra mayúscula (A-Z)');

    if (/[0-9]/.test(password)) score += 1;
    else feedback.push('Al menos un número (0-9)');

    if (/[@$!%*?&]/.test(password)) score += 1;
    else feedback.push('Al menos un carácter especial (@$!%*?&)');

    return { score, feedback };
  };

  // Validar contraseña en tiempo real
  useEffect(() => {
    if (formData.newPassword.length > 0) {
      setPasswordStrength(validatePasswordStrength(formData.newPassword));
    } else {
      setPasswordStrength({ score: 0, feedback: [] });
    }
  }, [formData.newPassword]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    // Validaciones
    if (formData.newPassword !== formData.confirmNewPassword) {
      setError('Las contraseñas no coinciden');
      setLoading(false);
      return;
    }

    if (formData.newPassword.length < 8) {
      setError('La contraseña debe tener al menos 8 caracteres');
      setLoading(false);
      return;
    }

    // Validar que cumpla todos los requisitos
    const strength = validatePasswordStrength(formData.newPassword);
    if (strength.score < 5) {
      setError(`La contraseña no cumple con los requisitos de seguridad: ${strength.feedback.join(', ')}`);
      setLoading(false);
      return;
    }

    try {
      // CAMBIAR CONTRASEÑA - PATCH /users/change-password
      const response = await fetch(`http://localhost:3000/users/change-password`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          newPassword: formData.newPassword
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 403) {
          throw new Error('No tienes permisos para cambiar esta contraseña');
        } else if (response.status === 404) {
          throw new Error('Usuario no encontrado');
        } else if (response.status === 400) {
          throw new Error(errorData.message || 'La contraseña no cumple con los estándares de seguridad');
        } else {
          throw new Error(errorData.message || 'Error al cambiar la contraseña');
        }
      }

      setSuccess('Contraseña cambiada correctamente');
      
      // Limpiar formulario
      setFormData({
        newPassword: '',
        confirmNewPassword: ''
      });

      // Redirigir después de 2 segundos
      setTimeout(() => {
        if (isOwnPassword) {
          navigate('/dashboard');
        } else {
          navigate('/users');
        }
      }, 2000);

    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const getTitle = () => {
    if (isOwnPassword) return 'Cambiar Mi Contraseña';
    return 'Cambiar Contraseña de Usuario';
  };

  const getSubtitle = () => {
    if (isOwnPassword) return 'Actualiza tu contraseña de acceso';
    return 'Cambia la contraseña de este usuario';
  };

  const getButtonText = () => {
    if (loading) return 'Cambiando Contraseña...';
    return 'Cambiar Contraseña';
  };

  const getCancelDestination = () => {
    if (isOwnPassword) return '/dashboard';
    return '/users';
  };

  return (
    <div className="relative min-h-screen">
      <div className="max-w-md mx-auto relative z-10 px-3 sm:px-4 lg:px-6">
        {/* Header */}
        <div className="mb-6 sm:mb-8 text-center">
          <h1 className={`text-3xl sm:text-4xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{getTitle()}</h1>
          <p className={`text-base sm:text-lg ${theme === 'dark' ? 'text-gray-200' : 'text-gray-600'} mt-2`}>
            {getSubtitle()}
          </p>
        </div>

        {/* Formulario con BorderBeam */}
        <div className={`relative backdrop-blur-xl shadow-2xl border p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl ${
          theme === 'dark' 
            ? 'bg-black/30 shadow-purple-500/20 border-purple-400/30' 
            : 'bg-white/30 shadow-blue-500/20 border-blue-400/30'
        }`}>
          <BorderBeam duration={8} size={100} />
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Nueva Contraseña */}
            <div>
              <label htmlFor="newPassword" className={`block text-sm sm:text-base font-medium mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-800'
              }`}>
                Nueva Contraseña
              </label>
              <input
                type="password"
                id="newPassword"
                name="newPassword"
                value={formData.newPassword}
                onChange={handleChange}
                required
                className={`w-full px-3 sm:px-4 py-2 sm:py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 h-10 sm:h-12 text-sm sm:text-base ${
                  theme === 'dark'
                    ? 'bg-black/40 border-purple-400/50 focus:border-blue-400/70 focus:ring-blue-400/50 text-white placeholder:text-gray-300'
                    : 'bg-white/40 border-blue-400/50 focus:border-purple-400/70 focus:ring-purple-400/50 text-gray-800 placeholder:text-gray-500'
                }`}
                placeholder="Mínimo 8 caracteres con mayúsculas, minúsculas, números y caracteres especiales"
              />
              
              {/* Indicador de fortaleza de contraseña */}
              {formData.newPassword.length > 0 && (
                <div className="space-y-2 mt-2">
                  <div className="flex gap-1">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <div
                        key={level}
                        className={`h-1 flex-1 rounded-full transition-all duration-300 ${
                          level <= passwordStrength.score
                            ? level <= 2 ? 'bg-red-400' : level <= 3 ? 'bg-yellow-400' : level <= 4 ? 'bg-blue-400' : 'bg-green-400'
                            : theme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  {passwordStrength.feedback.length > 0 && (
                    <div className={`text-xs sm:text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                      {passwordStrength.feedback.join(', ')}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Confirmar Nueva Contraseña */}
            <div>
              <label htmlFor="confirmNewPassword" className={`block text-sm sm:text-base font-medium mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-800'
              }`}>
                Confirmar Nueva Contraseña
              </label>
              <input
                type="password"
                id="confirmNewPassword"
                name="confirmNewPassword"
                value={formData.confirmNewPassword}
                onChange={handleChange}
                required
                className={`w-full px-3 sm:px-4 py-2 sm:py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 h-10 sm:h-12 text-sm sm:text-base ${
                  theme === 'dark'
                    ? 'bg-black/40 border-purple-400/50 focus:border-blue-400/70 focus:ring-blue-400/50 text-white placeholder:text-gray-300'
                    : 'bg-white/40 border-blue-400/50 focus:border-purple-400/70 focus:ring-purple-400/50 text-gray-800 placeholder:text-gray-500'
                }`}
                placeholder="Repite la nueva contraseña"
              />
            </div>

            {/* Requisitos de contraseña */}
            <div className={`p-3 sm:p-4 rounded-lg border ${
              theme === 'dark' 
                ? 'bg-black/40 border-purple-400/30' 
                : 'bg-white/40 border-blue-400/30'
            }`}>
              <h4 className={`text-sm sm:text-base font-medium mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-800'
              }`}>Requisitos de seguridad:</h4>
              <ul className={`text-xs sm:text-sm space-y-1 ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
                <li>• Mínimo 8 caracteres</li>
                <li>• Al menos 1 letra minúscula (a-z)</li>
                <li>• Al menos 1 letra mayúscula (A-Z)</li>
                <li>• Al menos 1 número (0-9)</li>
                <li>• Al menos 1 carácter especial (@$!%*?&)</li>
              </ul>
            </div>

            {/* Mensajes de error y éxito */}
            {error && (
              <div className={`border px-3 sm:px-4 py-2 sm:py-3 rounded-md ${
                theme === 'dark'
                  ? 'bg-red-900/50 border-red-700/50 text-red-200'
                  : 'bg-red-100 border-red-300 text-red-800'
              }`}>
                {error}
              </div>
            )}

            {success && (
              <div className={`border px-3 sm:px-4 py-2 sm:py-3 rounded-md ${
                theme === 'dark'
                  ? 'bg-green-900/50 border-green-700/50 text-green-200'
                  : 'bg-green-100 border-green-300 text-green-800'
              }`}>
                {success}
              </div>
            )}

            {/* Botones con ShimmerButton */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 pt-4">
              <ShimmerButton 
                onClick={handleSubmit}
                disabled={loading}
                className="flex-1 shadow-2xl"
              >
                <span className="whitespace-pre-wrap text-center text-sm sm:text-base font-medium leading-none tracking-tight text-white">
                  {getButtonText()}
                </span>
              </ShimmerButton>
              
              <ShimmerButton 
                onClick={() => navigate(getCancelDestination())}
                className="flex-1 shadow-2xl"
              >
                <span className="whitespace-pre-wrap text-center text-sm sm:text-base font-medium leading-none tracking-tight text-white">
                  Cancelar
                </span>
              </ShimmerButton>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
