import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { InteractiveHoverButton } from "@/components/magicui/interactive-hover-button";
import { BlurFade } from "@/components/magicui/blur-fade";
import { ShineBorder } from "@/components/magicui/shine-border";
import { AuroraBackground } from "@/components/ui/aurora-background";
import { AuroraText } from "@/components/magicui/aurora-text";
import { useTheme } from '../hooks/useTheme';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  const [attempts, setAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const { login } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const navigate = useNavigate();
  
  // Referencias
  const themeToggleRef = useRef(null);
  const emailInputRef = useRef(null);
  const passwordInputRef = useRef(null);

  // Configuración de temas
  const themes = {
    dark: {
      background: 'from-black via-gray-900 via-cyan-900/20 to-black',
      container: 'bg-black/20 backdrop-blur-xl shadow-2xl shadow-purple-500/20',
      input: 'bg-black/30 border-purple-400/50 focus:border-blue-400/70',
      text: 'text-white',
      subtitle: 'text-gray-300',
      label: 'text-gray-300',
      particles: 'bg-cyan-400'
    },
    light: {
      background: 'from-blue-50 via-cyan-100 via-blue-200 to-indigo-100',
      container: 'bg-white/20 backdrop-blur-xl shadow-2xl shadow-blue-500/20',
      input: 'bg-white/30 border-blue-400/50 focus:border-purple-400/70',
      text: 'text-gray-800',
      subtitle: 'text-gray-600',
      label: 'text-gray-700',
      particles: 'bg-blue-500'
    }
  };

  const currentTheme = themes[theme] || themes.dark; // Fallback al tema oscuro si no se encuentra

  // Prevenir recarga de página
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (loading) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [loading]);

  // Prevenir navegación con teclas
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (loading && (e.key === 'F5' || (e.ctrlKey && e.key === 'r'))) {
        e.preventDefault();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [loading]);

  // Validaciones de seguridad
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) return 'El email es requerido';
    if (!emailRegex.test(email)) return 'Formato de email inválido';
    if (email.length > 254) return 'El email es demasiado largo';
    return null;
  };

  const validatePassword = (password) => {
    if (!password) return 'La contraseña es requerida';
    if (password.length < 6) return 'La contraseña debe tener al menos 6 caracteres';
    if (password.length > 128) return 'La contraseña es demasiado larga';
    return null;
  };

  const validateForm = () => {
    const errors = {};
    
    const emailError = validateEmail(email);
    if (emailError) errors.email = emailError;
    
    const passwordError = validatePassword(password);
    if (passwordError) errors.password = passwordError;

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // La función toggleTheme ahora viene del contexto ThemeContext

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Prevenir múltiples envíos
    if (loading || isLocked) return;
    
    // Limpiar errores previos
    setError('');
    setValidationErrors({});
    
    // Validar formulario
    if (!validateForm()) {
      // Enfocar el primer campo con error
      if (validationErrors.email) {
        emailInputRef.current?.focus();
      } else if (validationErrors.password) {
        passwordInputRef.current?.focus();
      }
      return;
    }

    setLoading(true);

    try {
      const result = await login(email, password);
      
      if (result.success) {
        setSuccess('¡Sesión iniciada con éxito!');
        // Resetear intentos fallidos
        setAttempts(0);
        // Limpiar campos del formulario
        setEmail('');
        setPassword('');
        // Redirigir después de mostrar el mensaje de éxito
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000); // 2 segundos de delay
      } else {
          // Incrementar intentos fallidos
          const newAttempts = attempts + 1;
          setAttempts(newAttempts);
          
          // Bloquear después de 5 intentos fallidos
          if (newAttempts >= 5) {
            setIsLocked(true);
            setError('Demasiados intentos fallidos. Intenta de nuevo en 5 minutos.');
            setTimeout(() => {
              setIsLocked(false);
              setAttempts(0);
              setError('');
            }, 5 * 60 * 1000); // 5 minutos
          } else {
            // Mostrar mensaje genérico sin información específica del servidor
            setError('Credenciales inválidas. Verifica tu email y contraseña.');
          }
        }
    } catch {
      const newAttempts = attempts + 1;
      setAttempts(newAttempts);
      setError('Error al conectar con el servidor. Intenta de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  // Manejar cambios en los inputs
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    if (validationErrors.email) {
      setValidationErrors(prev => ({ ...prev, email: null }));
    }
  };

  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    if (validationErrors.password) {
      setValidationErrors(prev => ({ ...prev, password: null }));
    }
  };



  return (
    <AuroraBackground showRadialGradient={true} theme={theme}>
      {/* CSS para la animación de deslizamiento */}
      <style>{`
        @keyframes slideUp {
          0% {
            transform: translate(-50%, 100%) scale(0.8);
            opacity: 0;
            filter: blur(10px);
          }
          50% {
            transform: translate(-50%, -10px) scale(1.05);
            opacity: 0.8;
            filter: blur(2px);
          }
          100% {
            transform: translate(-50%, 0) scale(1);
            opacity: 1;
            filter: blur(0px);
          }
        }
        
        @keyframes shimmer {
          0% {
            background-position: -200% center;
          }
          100% {
            background-position: 200% center;
          }
        }
        
        .notification-toast {
          position: fixed;
          bottom: 2rem;
          left: 50%;
          transform: translateX(-50%);
          z-index: 9999;
          pointer-events: none;
        }
        
        .notification-toast .bg-gradient-to-r {
          background-size: 200% 100%;
          animation: shimmer 3s ease-in-out infinite;
        }
        
        .login-card {
          width: 95% !important;
          max-width: 700px !important;
          margin: 0 auto !important;
          transform: none !important;
          scale: 1 !important;
          transition: none !important;
          box-sizing: border-box !important;
          flex-shrink: 0 !important;
          flex-grow: 0 !important;
        }
        
        .login-card input, .login-card label, .login-card .card-title, .login-card .card-content {
          transform: none !important;
          scale: 1 !important;
          box-sizing: border-box !important;
        }
        
        .login-container {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      `}</style>
      
      {/* Sistema de notificaciones simplificado - Solo verde y rojo */}
      {(success || error) && (
        <div 
          className="notification-toast w-auto"
          style={{
            animation: 'slideUp 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards'
          }}
        >
          {/* Notificación de éxito (Verde) */}
          {success && (
            <div className={`px-6 py-4 rounded-2xl border-2 shadow-xl backdrop-blur-xl transition-all duration-700 min-w-max relative overflow-hidden ${
              theme === 'dark' 
                ? 'text-emerald-200 bg-gradient-to-r from-emerald-900/95 via-emerald-800/90 to-emerald-900/95 border-emerald-400/60 shadow-emerald-400/30' 
                : 'text-emerald-700 bg-gradient-to-r from-emerald-50/98 via-emerald-100/95 to-emerald-50/98 border-emerald-400/70 shadow-emerald-400/40'
            }`}>
              {/* Efecto de brillo sutil */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-emerald-400/10 to-transparent animate-pulse"></div>
              
              {/* Contenido de la notificación */}
              <div className="flex items-center justify-center space-x-3 relative z-10">
                {/* Icono de éxito animado */}
                <div className="relative">
                  <div className="w-5 h-5 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full animate-pulse shadow-lg shadow-emerald-400/50 flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  {/* Anillo exterior pulsante */}
                  <div className="absolute inset-0 w-5 h-5 border-2 border-emerald-400/50 rounded-full animate-ping"></div>
                </div>
                
                {/* Mensaje de éxito */}
                <span className="font-semibold text-base tracking-wide whitespace-nowrap bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent">
                  {success}
                </span>
                
                {/* Icono de éxito animado (espejo) */}
                <div className="relative">
                  <div className="w-5 h-5 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full animate-pulse shadow-lg shadow-emerald-400/50 flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  {/* Anillo exterior pulsante */}
                  <div className="absolute inset-0 w-5 h-5 border-2 border-emerald-400/50 rounded-full animate-ping"></div>
                </div>
              </div>
              
              {/* Línea de progreso sutil */}
              <div className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-emerald-400 via-emerald-500 to-emerald-400 rounded-b-2xl animate-pulse"></div>
            </div>
          )}

          {/* Notificación de error (Roja) - Incluye mensaje de intentos restantes */}
          {!success && error && (
            <div className={`px-6 py-4 rounded-2xl border-2 shadow-xl backdrop-blur-xl transition-all duration-700 min-w-max relative overflow-hidden ${
              theme === 'dark' 
                ? 'text-red-200 bg-gradient-to-r from-red-900/95 via-red-800/90 to-red-900/95 border-red-400/60 shadow-red-400/30' 
                : 'text-red-700 bg-gradient-to-r from-red-50/98 via-red-100/95 to-red-50/98 border-red-400/70 shadow-red-400/40'
            }`}>
              {/* Efecto de brillo sutil */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-red-400/10 to-transparent animate-pulse"></div>
              
              {/* Contenido del error */}
              <div className="flex flex-col items-center justify-center space-y-2 relative z-10">
                {/* Mensaje principal de error */}
                <div className="flex items-center justify-center space-x-3">
                  {/* Icono de error animado */}
                  <div className="relative">
                    <div className="w-5 h-5 bg-gradient-to-r from-red-400 to-red-500 rounded-full animate-pulse shadow-lg shadow-red-400/50 flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    {/* Anillo exterior pulsante */}
                    <div className="absolute inset-0 w-5 h-5 border-2 border-red-400/50 rounded-full animate-ping"></div>
                  </div>
                  
                  {/* Mensaje de error */}
                  <span className="font-semibold text-base tracking-wide whitespace-nowrap bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent">
                    {error}
                  </span>
                  
                  {/* Icono de error animado (espejo) */}
                  <div className="relative">
                    <div className="w-5 h-5 bg-gradient-to-r from-red-400 to-red-500 rounded-full animate-pulse shadow-lg shadow-red-400/50 flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    {/* Anillo exterior pulsante */}
                    <div className="absolute inset-0 w-5 h-5 border-2 border-red-400/50 rounded-full animate-ping"></div>
                  </div>
                </div>
                
                {/* Mensaje de intentos restantes - Solo mostrar después de 2 intentos */}
                {attempts >= 2 && attempts < 5 && (
                  <div className="flex items-center justify-center space-x-2">
                    <svg className="w-4 h-4 text-red-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium text-red-300/80">
                      Cuidado: {5 - attempts} intentos restantes antes del bloqueo
                    </span>
                  </div>
                )}
              </div>
              
              {/* Línea de progreso sutil */}
              <div className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-red-400 via-red-500 to-red-400 rounded-b-2xl animate-pulse"></div>
            </div>
          )}
        </div>
      )}

      {/* Botón de cambio de tema - Responsivo */}
      <button
        ref={themeToggleRef}
        onClick={toggleTheme}
        className="absolute top-4 left-4 sm:top-6 sm:left-6 z-30 p-2 sm:p-3 rounded-full bg-gray-800/30 backdrop-blur-sm border border-gray-700/30 hover:bg-gray-700/40 hover:border-gray-600/50 transition-all duration-200 group"
      >
        <div className="w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center">
          {theme === 'dark' ? (
            <div className="w-3 h-3 sm:w-4 sm:h-4 bg-yellow-400 rounded-full shadow-lg shadow-yellow-400/50" />
          ) : (
            <div className="w-3 h-3 sm:w-4 sm:h-4 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50" />
          )}
        </div>
        <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap hidden sm:block">
          {theme === 'dark' ? 'Modo Día' : 'Modo Noche'}
        </span>
      </button>

      {/* Card de login con Magic UI - Completamente responsivo */}
      <div className="login-container">
        <Card className={`login-card relative overflow-hidden ${currentTheme.container}`}>
          {/* ShineBorder con colores que combinen con el AuroraBackground */}
          <ShineBorder 
            shineColor={
              theme === 'dark' 
                ? ["#A07CFE", "#FE8FB5", "#FFBE7B"] // Púrpura, rosa, naranja para modo oscuro
                : ["#3B82F6", "#8B5CF6", "#06B6D4"] // Azul, púrpura, cyan para modo claro
            } 
          />
          
          {/* Layout responsivo: vertical en móvil, horizontal en tablet/desktop */}
          <div className="flex flex-col lg:flex-row">
            {/* Lado izquierdo - Logo y título */}
            <div className="w-full lg:w-1/2 p-4 sm:p-6 lg:p-8 flex flex-col justify-center items-center border-b lg:border-b-0 lg:border-r border-gray-600/30">
              {/* Logo de Sequre Quantum - Responsivo */}
              <div className="flex justify-center mb-4 sm:mb-6">
                <img 
                  src="/src/assets/sequre-logo-negro-2.svg"
                  alt="Sequre Quantum Logo" 
                  className={`h-16 w-auto sm:h-20 lg:h-24 transition-all duration-500 ${
                    theme === 'dark' 
                      ? 'filter brightness-0 invert' // Hace el logo blanco en modo oscuro
                      : 'filter brightness-0' // Mantiene el logo negro en modo claro
                  }`}
                />
              </div>
              
              {/* Títulos responsivos */}
              <CardTitle className={`text-xl sm:text-2xl lg:text-3xl font-bold ${currentTheme.text} mb-2 sm:mb-3 text-center`}>
                Bienvenido(a) a
              </CardTitle>
              <CardTitle className={`text-2xl sm:text-3xl lg:text-4xl font-bold ${currentTheme.text} mb-3 sm:mb-4 text-center`}>
                <AuroraText colors={theme === 'dark' ? ["#A07CFE", "#FE8FB5", "#FFBE7B"] : ["#3B82F6", "#8B5CF6", "#06B6D4"]}>
                  QRNG Raffle
                </AuroraText>
              </CardTitle>
            </div>

            {/* Lado derecho - Formulario */}
            <div className="w-full lg:w-1/2 p-4 sm:p-6 lg:p-8">
              <form onSubmit={handleSubmit} noValidate>
                <div className="grid gap-4 sm:gap-6">
                  <div className="grid gap-2 sm:gap-3">
                    <Label htmlFor="email" className={`${currentTheme.label} text-sm sm:text-base font-medium`}>
                      Email
                    </Label>
                    <Input 
                      ref={emailInputRef}
                      id="email" 
                      type="email" 
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={handleEmailChange}
                      className={`${currentTheme.input} ${currentTheme.text} placeholder:text-gray-400 focus:border-cyan-400/50 transition-all duration-200 h-10 sm:h-12 text-sm sm:text-base ${
                        validationErrors.email ? 'border-red-400 focus:border-red-400' : ''
                      }`}
                      required
                      disabled={isLocked}
                      autoComplete="email"
                    />
                    {validationErrors.email && (
                      <span className="text-red-400 text-xs">{validationErrors.email}</span>
                    )}
                  </div>
                  
                  <div className="grid gap-2 sm:gap-3">
                    <Label htmlFor="password" className={`${currentTheme.label} text-sm sm:text-base font-medium`}>
                      Contraseña
                    </Label>
                    <Input 
                      ref={passwordInputRef}
                      id="password" 
                      type="password"
                      value={password}
                      onChange={handlePasswordChange}
                      className={`${currentTheme.input} ${currentTheme.text} placeholder:text-gray-400 focus:border-cyan-400/50 transition-all duration-200 h-10 sm:h-12 text-sm sm:text-base ${
                        validationErrors.password ? 'border-red-400 focus:border-red-400' : ''
                      }`}
                      required
                      disabled={isLocked}
                      autoComplete="current-password"
                    />
                    {validationErrors.password && (
                      <span className="text-red-400 text-xs">{validationErrors.password}</span>
                    )}
                  </div>
                  
                  {/* El mensaje de éxito ahora aparece como notificación flotante */}

                  {/* Mensaje de error responsivo - Versión elegante y moderna */}
                  {/* Mensaje de error responsivo - Versión elegante y moderna */}

                  {/* Información de intentos fallidos - Versión elegante y moderna */}
                  {/* Información de intentos fallidos - Versión elegante y moderna */}
                  
                  {/* Botón responsivo */}
                  <div className="flex justify-center pt-2">
                    <InteractiveHoverButton 
                      className="w-full sm:w-auto px-6 sm:px-8 py-2 sm:py-3 text-base sm:text-lg font-semibold shadow-2xl text-white border-0 transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{
                        background: theme === 'dark' 
                          ? 'linear-gradient(135deg, #A07CFE 0%, #FE8FB5 50%, #FFBE7B 100%)' // Púrpura, rosa, naranja para modo oscuro
                          : 'linear-gradient(135deg, #3B82F6 0%, #8B5CF6 50%, #06B6D4 100%)', // Azul, púrpura, cyan para modo claro
                        boxShadow: theme === 'dark'
                          ? '0 10px 25px -5px rgba(160, 124, 254, 0.4), 0 4px 6px -2px rgba(254, 143, 181, 0.2)' // Sombra púrpura
                          : '0 10px 25px -5px rgba(59, 130, 246, 0.4), 0 4px 6px -2px rgba(139, 92, 246, 0.2)' // Sombra azul
                      }}
                      onClick={handleSubmit}
                      disabled={loading || isLocked || success}
                    >
                      {success ? '¡Redirigiendo...!' : loading ? 'Iniciando sesión...' : isLocked ? 'Formulario Bloqueado' : 'Iniciar Sesión'}
                    </InteractiveHoverButton>
                  </div>


                </div>
              </form>
            </div>
          </div>
        </Card>
      </div>
    </AuroraBackground>
  );
};

export default Login;
