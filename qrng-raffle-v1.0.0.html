<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>QRNG raffle</title>
        <!-- Link to external CSS file (optional) -->
        <!-- <link rel="stylesheet" href="styles.css"> -->
    </head>
    <body>
        <h1>Demo de Tómbola Cuántica</h1>
        
        <div id="messages_id"></div>

        <br>
        <br>

        <div>Ganador Actual:</div>
        <p id="winner_id">&nbsp;</p>

        <button id="button_new_draw_id">
            Nuevo Sorteo
        </button>

        <br>
        <br>

        <button id="button_erase_past_winners_id">
            Borrar Ganadores Pasados
        </button>

        <br>
        <br>
        
        <div>Ganadores Pasados:</div>
        <div id="previous_winners_id"></div>
        
        <script defer>

            let seats = {}, start_number_to_assign_to_seats = 1, number_assigned_to_one_seat = 1, total_seats = 0;

            function add_row(letter, number_start, number_end)
            {
                for (let i = number_start; i <= number_end; i++)
                    seats[number_assigned_to_one_seat++] = letter + i;
            }
            function create_seats_and_numbers_relation()
            {
                seats = {};
                number_assigned_to_one_seat = start_number_to_assign_to_seats;
                add_row('P23 A', 1, 32);
                add_row('P23 B', 1, 36);
                add_row('P23 C', 1, 34);
                add_row('P23 D', 1, 36);
                add_row('P23 E', 1, 34);
                add_row('P23 F', 1, 36);
                add_row('P23 G', 1, 34);
                add_row('P23 H', 1, 36);
                add_row('P23 I', 1, 34);
                add_row('P23 J', 1, 30);
                add_row('P23 K', 1, 18);
                add_row('P23 L', 1, 34);
                add_row('P23 M', 1, 34);
                add_row('P23 N', 1, 34);
                add_row('P23 Ñ', 1, 34);
                add_row('P23 O', 1, 34);
                add_row('P23 P', 1, 34);
                add_row('P23 Q', 1, 34);
                add_row('P23 R', 1, 34);
                add_row('P23 S', 1, 34);
                add_row('P23 T', 1, 34);
                add_row('P23 U', 1, 34);
                add_row('P23 V', 1, 34);

                add_row('P4 A', 1, 36);
                add_row('P4 B', 1, 36);
                add_row('P4 C', 1, 36);
                add_row('P4 D', 1, 36);

                add_row('P5 E', 1, 36);
                add_row('P5 F', 1, 36);
                add_row('P5 G', 1, 36);
                add_row('P5 H', 1, 36);

                add_row('P6 I', 1, 36);
                add_row('P6 J', 1, 36);
                add_row('P6 K', 1, 36);
                add_row('P6 L', 1, 36);
                total_seats = Object.keys(seats).length;
                // console.log(total_seats);
            }
            
            create_seats_and_numbers_relation();

            let button_new_draw = document.getElementById("button_new_draw_id");
            button_new_draw.addEventListener('click', () => { new_draw_clicked(); });

            let messages_container = document.getElementById('messages_id');
            let winner_container = document.getElementById('winner_id');
            let previous_winners_container = document.getElementById('previous_winners_id');

            let button_erase_past_winners = document.getElementById('button_erase_past_winners_id');
            button_erase_past_winners.addEventListener('click', () => { previous_winners_container.textContent = ''; });

            let last_index = -1, draw_seats = [];
            function new_draw_clicked()
            {
                messages_container.textContent = '';
                buttons_enabled(false);

                const headers = new Headers();
                headers.append("Accept", "application/json");
                // IMPORTANT: In the production version do not set the token in the code.
                // Load it from somewhere else, for example, from an .env file.
                headers.append("authorization", "Bearer 7|tJUsZQFk96Y6l3jF8hmH5lC5sh1vI7EpE7KMjY3Z");

                let draws_quantity = 1;
                const form_data = new FormData();
                form_data.append("request_type", "draw");
                form_data.append("draws_quantity", draws_quantity);
                form_data.append("extractions_quantity", "100");
                form_data.append("range_start", start_number_to_assign_to_seats);
                form_data.append("range_end", total_seats);
                form_data.append("with_reposition", "0");

                const requestOptions = {
                    method: "POST",
                    headers: headers,
                    body: form_data
                };

                fetch("https://sequrequantumeaas.com/api/v1/get_data", requestOptions)
                    .then((response) => response.json()) // Gets the Response object
                    .then((result) => 
                    {
                        // Parses the body and gets the result

                        // console.log(result);
                        if('error_messages' in result)
                        {
                            // NOTE: There should not be input errors because here the input is set in the code.
                            Object.entries(result['error_messages']).forEach(([key, value]) =>
                            {
                                messages_container.innerHTML += `<p>Error: ${key}: ${value}</p>`;
                            });
                            buttons_enabled(true);
                            return;
                        }
                        let numbers_obj =
                        {
                            keys: ['success', 'draws_results', draws_quantity - 1, 'numbers'],
                            valid: false,
                            numbers: [],
                            get: function (result)
                            {
                                let obj = result;
                                for (let i = 0; i < this.keys.length; i++)
                                {
                                    let current_key = this.keys[i];
                                    if (!(current_key in obj))
                                    {
                                        this.valid = false;
                                        return;
                                    }
                                    obj = obj[current_key];
                                }
                                // TODO: see if to add more validations on how the string of numbers is received.
                                this.numbers = obj.split("-");
                                this.valid = true;
                            }
                        }
                        numbers_obj.get(result);
                        if (!numbers_obj.valid)
                        {
                            // TODO: see if to improve this.
                            messages_container.textContent = JSON.stringify(result);
                            buttons_enabled(true);
                            return;
                        }
                        do_winner_animation(numbers_obj.numbers);
                        
                    })
                    .catch((error) => 
                    {
                        // TODO: see if to improve this.
                        console.error(error);
                        messages_container.innerHTML += JSON.stringify(error);
                        buttons_enabled(true);
                        return;
                    });
            }

            let index = 0;
            function show_number()
            {
                winner_container.textContent = draw_seats[index++];
                let draw_seats_length = draw_seats.length;
                if(index < draw_seats_length)
                {
                    setTimeout(function ()
                    {
                        show_number();
                    }, 410 * ease_in_circ(index / draw_seats_length));
                }
                else
                {
                    post_winner_animation();
                }
            }
            // https://easings.net/#easeInCirc
            function ease_in_circ(x)
            {
                return 1 - Math.sqrt(1 - Math.pow(x, 2));
            }
            function do_winner_animation(numbers_array)
            {
                last_index = parseInt(numbers_array[numbers_array.length - 1]);
                draw_seats = [];
                numbers_array.forEach((number) => {
                    draw_seats.push(seats[parseInt(number)]);
                });

                index = 0;
                show_number();
            }
            function post_winner_animation()
            {
                let winner = seats[last_index];

                winner_container.innerHTML = '<b>' + winner + '</b>';

                let previous_winners = previous_winners_container.textContent.trim();
                // The last number is added first.
                if(previous_winners === '')
                    previous_winners_container.textContent = winner;
                else
                    previous_winners_container.textContent = winner + ', ' + previous_winners;

                buttons_enabled(true);
            }
            function buttons_enabled(are_enabled)
            {
                button_new_draw.disabled = !are_enabled;
                button_erase_past_winners.disabled = !are_enabled;
            }
        </script>

    </body>
</html>
