import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';

const UserList = () => {
  const navigate = useNavigate();
  const { token } = useAuth();
  
  // Refs para GSAP
  const containerRef = useRef(null);
  const headerRef = useRef(null);
  const tableRef = useRef(null);
  const cardRef = useRef(null);
  
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Animaciones GSAP
  useEffect(() => {
    const tl = gsap.timeline();
    
    // Header con efecto de aparición elegante
    tl.fromTo(headerRef.current,
      { y: -30, opacity: 0, scale: 0.95 },
      { y: 0, opacity: 1, scale: 1, duration: 1.0, ease: "power3.out" }
    );
    
    // Card principal con efecto de aparición suave
    tl.fromTo(cardRef.current,
      { y: 50, opacity: 0, scale: 0.9 },
      { y: 0, opacity: 1, scale: 1, duration: 1.2, ease: "power2.out" },
      "-=0.8"
    );
    
    // Tabla con efecto stagger
    if (tableRef.current) {
      tl.fromTo(tableRef.current.children,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, stagger: 0.05, ease: "power2.out" },
        "-=0.8"
      );
    }
    
    tl.delay(0.2);
  }, [users]);

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3000/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al cargar usuarios');
      }

      const data = await response.json();
      setUsers(data);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [token]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleDeleteUser = async (userId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar este usuario?')) {
      return;
    }

    setDeleteLoading(userId);
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`http://localhost:3000/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al eliminar usuario');
      }

      setSuccess('Usuario eliminado correctamente');
      setUsers(users.filter(user => user.id !== userId));
      
      setTimeout(() => setSuccess(''), 3000);
      
    } catch (error) {
      setError(error.message);
      setTimeout(() => setError(''), 3000);
    } finally {
      setDeleteLoading(null);
    }
  };

  const getRoleBadge = (role) => {
    const roleConfig = {
      admin: { 
        color: 'bg-gradient-to-r from-red-600 to-red-700 text-red-100 border-red-500/50', 
        text: 'Administrador',
        icon: '👑'
      },
      user: { 
        color: 'bg-gradient-to-r from-blue-600 to-blue-700 text-blue-100 border-blue-500/50', 
        text: 'Usuario',
        icon: '👤'
      }
    };
    
    const config = roleConfig[role] || roleConfig.user;
    return (
      <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium border ${config.color} shadow-lg`}>
        <span className="mr-1">{config.icon}</span>
        {config.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="w-full max-w-6xl mx-auto px-4 sm:px-0">
        <div className="text-center py-20">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-400 mx-auto"></div>
            <div className="absolute inset-0 rounded-full border-2 border-slate-700/30"></div>
          </div>
          <p className="mt-6 text-slate-300 text-lg">Cargando usuarios...</p>
          <p className="text-slate-400 text-sm">Preparando la lista de usuarios del sistema</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="w-full max-w-6xl mx-auto px-4 sm:px-0">
      {/* Header elegante */}
      <div ref={headerRef} className="mb-8 text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-3">
          Gestión de Usuarios
        </h1>
        <p className="text-lg text-slate-300 mt-2 max-w-2xl mx-auto">
          Administra y gestiona todos los usuarios del sistema SECURE
        </p>
      </div>

      {/* Card principal elegante */}
      <div ref={cardRef} className="bg-slate-800/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-700/50 overflow-hidden">
        {/* Header de la card */}
        <div className="bg-gradient-to-r from-slate-700/50 to-slate-600/50 px-8 py-6 border-b border-slate-600/50">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h2 className="text-xl font-semibold text-white">Lista de Usuarios</h2>
              <p className="text-slate-300 text-sm mt-1">Gestiona todos los usuarios del sistema</p>
            </div>
            <button
              onClick={() => navigate('/users/create')}
              className="mt-4 sm:mt-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl border border-blue-500/50 flex items-center"
            >
              <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Crear Usuario
            </button>
          </div>
        </div>
        
        {/* Contenido de la tabla */}
        <div className="p-8">
          {/* Mensajes de error y éxito */}
          {error && (
            <div className="bg-red-900/30 border border-red-700/50 text-red-200 px-6 py-4 rounded-xl backdrop-blur-sm mb-6">
              <div className="flex items-center">
                <svg className="h-5 w-5 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {error}
              </div>
            </div>
          )}

          {success && (
            <div className="bg-green-900/30 border border-green-700/50 text-green-200 px-6 py-4 rounded-xl backdrop-blur-sm mb-6">
              <div className="flex items-center">
                <svg className="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {success}
              </div>
            </div>
          )}

          {/* Tabla de usuarios */}
          <div className="overflow-hidden rounded-xl border border-slate-600/50 bg-slate-700/30">
            <div className="overflow-x-auto">
              <table ref={tableRef} className="min-w-full divide-y divide-slate-600/50">
                <thead className="bg-gradient-to-r from-slate-700/80 to-slate-600/80">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider transition-colors duration-300">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Usuario
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider transition-colors duration-300">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Correo
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider transition-colors duration-300">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Rol
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider transition-colors duration-300">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                        </svg>
                        Acciones
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-slate-700/20 divide-y divide-slate-600/50">
                  {users.map((user) => (
                    <tr key={user.id} className="transition-all duration-300 hover:bg-slate-600/30 group">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full flex items-center justify-center text-white font-semibold text-sm ring-2 ring-slate-500/30 group-hover:ring-slate-400/50 transition-all duration-300">
                            {user.name?.charAt(0) || user.email?.charAt(0) || 'U'}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-white group-hover:text-blue-300 transition-colors duration-300">
                              {user.name || 'Sin nombre'}
                            </div>
                            <div className="text-xs text-slate-400">
                              ID: {user.id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-slate-300 group-hover:text-white transition-colors duration-300">
                          {user.email}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getRoleBadge(user.role)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => navigate(`/users/${user.id}`)}
                            className="text-blue-400 hover:text-blue-300 transition-colors duration-300 p-2 rounded-lg hover:bg-blue-500/10"
                            title="Editar usuario"
                          >
                            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            disabled={deleteLoading === user.id}
                            className="text-red-400 hover:text-red-300 transition-colors duration-300 p-2 rounded-lg hover:bg-red-500/10 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Eliminar usuario"
                          >
                            {deleteLoading === user.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-400"></div>
                            ) : (
                              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Mensaje cuando no hay usuarios */}
          {users.length === 0 && !loading && (
            <div className="text-center py-12">
              <div className="text-slate-400 mb-4">
                <svg className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-slate-300 mb-2">No hay usuarios registrados</h3>
              <p className="text-slate-400 mb-6">Comienza creando el primer usuario del sistema</p>
              <button
                onClick={() => navigate('/users/create')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Crear Primer Usuario
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserList;
