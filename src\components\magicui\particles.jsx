"use client";

import { useEffect, useRef, useCallback } from "react";

export const Particles = ({
  className = "",
  quantity = 100,
  ease = 50,
  refresh,
  color = "#ffffff",
}) => {
  const canvasRef = useRef(null);
  const canvasContainerRef = useRef(null);
  const context = useRef(null);
  const circles = useRef([]);
  const mousePosition = useRef({ x: 0, y: 0 });
  const mouse = useRef({ x: 0, y: 0 });
  const canvasSize = useRef({ w: 0, h: 0 });
  const dpr = typeof window !== "undefined" ? window.devicePixelRatio : 1;

  useEffect(() => {
    if (canvasRef.current) {
      context.current = canvasRef.current.getContext("2d");
    }
    initCanvas();
    animate();
    window.addEventListener("resize", initCanvas);

    return () => {
      window.removeEventListener("resize", initCanvas);
    };
  }, [initCanvas, animate]);

  useEffect(() => {
    onMouseMove();
    window.addEventListener("mousemove", onMouseMove);
    return () => {
      window.removeEventListener("mousemove", onMouseMove);
    };
  }, []);

  useEffect(() => {
    initCanvas();
  }, [refresh, initCanvas]);

  const initCanvas = useCallback(() => {
    if (canvasContainerRef.current && canvasRef.current && context.current) {
      const resizeCanvas = () => {
        if (canvasContainerRef.current && canvasRef.current && context.current) {
          circles.current.length = 0;
          canvasSize.current.w = canvasContainerRef.current.offsetWidth;
          canvasSize.current.h = canvasContainerRef.current.offsetHeight;
          canvasRef.current.width = canvasSize.current.w * dpr;
          canvasRef.current.height = canvasSize.current.h * dpr;
          canvasRef.current.style.width = canvasSize.current.w + "px";
          canvasRef.current.style.height = canvasSize.current.h + "px";
          context.current.scale(dpr, dpr);
        }
      };

             const circleParams = () => {
         return {
           x: Math.random() * canvasSize.current.w,
           y: Math.random() * canvasSize.current.h,
           translateX: 0,
           translateY: 0,
           size: Math.random() * 2,
           alpha: 0,
           targetAlpha: parseFloat((Math.random() * 0.6 + 0.1).toFixed(2)),
           hex: color,
           rgb: hexToRgb(color),
         };
       };

       const drawParticles = () => {
         let particleCount = quantity;
         for (let _ = 0; _ < particleCount; _++) {
           const circle = circleParams();
           circles.current.push(circle);
         }
       };

      resizeCanvas();
      drawParticles();
    }
  }, [quantity, dpr, color]);

  const onMouseMove = () => {
    if (canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      const { w, h } = canvasSize.current;
      const x = mousePosition.current.x - rect.left - w / 2;
      const y = mousePosition.current.y - rect.top - h / 2;
      const inside = x < w / 2 && x > -w / 2 && y < h / 2 && y > -h / 2;
      if (inside) {
        mouse.current.x = x;
        mouse.current.y = y;
      }
    }
  };





  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  };





  const animate = useCallback(() => {
    if (context.current) {
      context.current.clearRect(
        0,
        0,
        canvasSize.current.w,
        canvasSize.current.h
      );
      circles.current.forEach((circle) => {


        circle.alpha = circle.alpha + (circle.targetAlpha - circle.alpha) / ease;
        context.current.globalAlpha = circle.alpha;
        context.current.beginPath();
        context.current.arc(
          circle.x + circle.translateX,
          circle.y + circle.translateY,
          circle.size,
          0,
          2 * Math.PI
        );
        context.current.fillStyle = `rgb(${circle.rgb.r}, ${circle.rgb.g}, ${circle.rgb.b})`;
        context.current.fill();

        if (mouse.current.x && mouse.current.y) {
          const dx = mouse.current.x - (circle.x + circle.translateX);
          const dy = mouse.current.y - (circle.y + circle.translateY);
          const distance = Math.sqrt(dx * dx + dy * dy);
          if (distance < 100) {
            const force = (100 - distance) / 100;
            const direction = {
              x: dx / distance,
              y: dy / distance,
            };
            circle.translateX += direction.x * force * 2;
            circle.translateY += direction.y * force * 2;
          }
        }

        circle.translateX += (Math.random() - 0.5) * 0.5;
        circle.translateY += (Math.random() - 0.5) * 0.5;

        if (
          circle.x + circle.translateX < 0 ||
          circle.x + circle.translateX > canvasSize.current.w ||
          circle.y + circle.translateY < 0 ||
          circle.y + circle.translateY > canvasSize.current.h
        ) {
          circle.translateX = 0;
          circle.translateY = 0;
        }
      });
    }
    requestAnimationFrame(animate);
  }, [ease]);

  return (
    <div className={className} ref={canvasContainerRef}>
      <canvas
        className="pointer-events-none absolute inset-0 z-0"
        ref={canvasRef}
      />
    </div>
  );
};
