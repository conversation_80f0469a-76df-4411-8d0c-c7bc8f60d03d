import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { HoveredLink, Menu, MenuItem } from './ui/navbar-menu';
import { cn } from '../lib/utils';
import { useTheme } from '../hooks/useTheme';
import { AuroraBackground } from './ui/aurora-background';


// Registrar GSAP
gsap.registerPlugin();

const Layout = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();
  
     // Refs para GSAP
   const logoRef = useRef(null);
   const userSectionRef = useRef(null);

  const mobileMenuRef = useRef(null);
  const hamburgerRef = useRef(null);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleDropdown = (dropdown) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Animaciones GSAP
  useEffect(() => {
    const tl = gsap.timeline();

         // Animación de entrada del navbar
     tl.fromTo('.navbar-unified',
       { y: -100, opacity: 0 },
       { y: 0, opacity: 1, duration: 1.2, ease: "power3.out" }
     );

         // Animación del logo
     tl.fromTo(logoRef.current,
       { scale: 0, opacity: 0, y: -20 },
       { scale: 1, opacity: 1, y: 0, duration: 1.0, ease: "power3.out" },
       "-=0.6"
     );

         // Animación de los elementos de navegación (navbar central)
     tl.fromTo('.navbar-menu',
       { y: 30, opacity: 0 },
       { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
       "-=0.4"
     );

    // Animación de la sección de usuario
    tl.fromTo(userSectionRef.current,
      { x: 50, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
      "-=0.3"
    );



  }, []);

  // Animación del menú móvil
  useEffect(() => {
    if (mobileMenuRef.current) {
      if (isMobileMenuOpen) {
        gsap.fromTo(mobileMenuRef.current,
          { x: '100%', opacity: 0 },
          { x: 0, opacity: 1, duration: 0.5, ease: "power3.out" }
        );
        
        // Animación de los elementos del menú
        gsap.fromTo(mobileMenuRef.current.children,
          { y: 20, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.4, stagger: 0.1, ease: "power2.out" },
          "-=0.3"
        );
      } else {
        gsap.to(mobileMenuRef.current, {
          x: '100%',
          opacity: 0,
          duration: 0.3,
          ease: "power3.in"
        });
      }
    }
  }, [isMobileMenuOpen]);

  // Navigation array removed as users section is no longer needed

  return (
    <AuroraBackground showRadialGradient={true} theme={theme}>
             <div className={`w-full h-full transition-all duration-700 ease-out`}>
        {/* Navbar único integrado */}
        <div className="fixed top-4 sm:top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-4xl px-3 sm:px-4 lg:px-6 navbar-unified">
          
          {/* Botón de cambio de tema - Al lado del logo pero por fuera del navbar */}
          <div className="absolute -left-12 sm:-left-16 lg:-left-20 top-1/2 transform -translate-y-1/2">
            <button
              onClick={toggleTheme}
              className="p-1.5 sm:p-2 lg:p-3 rounded-full bg-gray-800/30 backdrop-blur-sm border border-gray-700/30 hover:bg-gray-700/40 hover:border-gray-600/50 transition-all duration-700 ease-out group"
            >
              <div className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 flex items-center justify-center">
                {theme === 'dark' ? (
                  <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 lg:w-3 lg:h-3 bg-yellow-400 rounded-full shadow-lg shadow-yellow-400/50" />
                ) : (
                  <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 lg:w-3 lg:h-3 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50" />
                )}
              </div>
              <span className="absolute -bottom-5 sm:-bottom-6 lg:-bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap hidden sm:block">
                {theme === 'dark' ? 'Modo Día' : 'Modo Noche'}
              </span>
            </button>
          </div>
          
          <div className={cn("relative rounded-full border transition-all duration-700 ease-out", 
            theme === 'dark' 
              ? "border-gray-700/50 bg-black/80 backdrop-blur-md shadow-lg" 
              : "border-blue-200/50 bg-white/80 backdrop-blur-md shadow-lg"
          )}>
            <div className="flex items-center justify-between px-4 sm:px-6 py-3 sm:py-5 h-12 sm:h-16">
             
             {/* Logo Sequre Quantum */}
             <div ref={logoRef} className="flex items-center space-x-2 sm:space-x-4">
               <img 
                 src="/src/assets/sequre-logo-negro-2.svg"
                 alt="Sequre Quantum Logo" 
                 className={`h-8 w-auto sm:h-10 transition-all duration-700 ease-out ${
                   theme === 'dark' ? 'filter brightness-0 invert' : 'filter brightness-0'
                 }`}
               />
             </div>

             {/* Navegación central - solo Panel */}
             <div className="hidden lg:flex items-center space-x-6 navbar-menu">
               <button
                 onClick={() => navigate('/dashboard')}
                 className={`text-sm font-medium transition-all duration-700 ease-out flex items-center h-full ${
                   location.pathname === '/dashboard'
                     ? (theme === 'dark' ? 'text-white' : 'text-black')
                     : (theme === 'dark' ? 'text-gray-300 hover:text-white' : 'text-black hover:text-gray-800')
                 }`}
               >
                 Panel
               </button>
             </div>

             {/* Usuario */}
             <div ref={userSectionRef} className="hidden lg:block relative">
               <button
                 onClick={() => toggleDropdown('user')}
                 className={`flex items-center px-3 py-2 transition-all duration-700 ease-out ${
                   theme === 'dark'
                     ? 'text-gray-300 hover:text-white'
                     : 'text-black hover:text-gray-800'
                 }`}
               >
                 <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold mr-2 text-xs sm:text-sm">
                   {user?.name?.charAt(0) || user?.email?.charAt(0) || 'A'}
                 </div>
                 <span className="font-medium text-xs sm:text-sm">
                   {user?.name || user?.email?.split('@')[0] || 'Admin1'}
                 </span>
                 <svg className="ml-2 h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                 </svg>
               </button>
               
               {/* User Dropdown */}
               {activeDropdown === 'user' && (
                 <div className="absolute right-0 z-50 mt-1 w-40 sm:w-48">
                   <div className={`backdrop-blur-xl border shadow-2xl py-2 transition-all duration-700 ease-out ${
                     theme === 'dark'
                       ? 'bg-gray-900/95 border-gray-700/50'
                       : 'bg-white/95 border-black/20'
                   }`}>
                     <button
                       onClick={() => {
                         navigate('/profile');
                         setActiveDropdown(null);
                       }}
                       className={`block w-full text-left px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm transition-all duration-700 ease-out ${
                         theme === 'dark'
                           ? 'text-gray-300 hover:text-white hover:bg-gray-800/50'
                           : 'text-black hover:text-gray-800 hover:bg-gray-100/50'
                       }`}
                     >
                       Perfil
                     </button>
                     <button
                       onClick={() => {
                         navigate('/change-password');
                         setActiveDropdown(null);
                       }}
                       className={`block w-full text-left px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm transition-all duration-700 ease-out ${
                         theme === 'dark'
                           ? 'text-gray-300 hover:text-white hover:bg-gray-800/50'
                           : 'text-black hover:text-gray-800 hover:bg-gray-100/50'
                       }`}
                     >
                       Configuración
                     </button>
                     <div className={`border-t my-1 sm:my-2 transition-all duration-700 ease-out ${
                       theme === 'dark' ? 'border-gray-600/30' : 'border-gray-300/30'
                     }`}></div>
                     <button
                       onClick={handleLogout}
                       className="block w-full text-left px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-700 ease-out"
                     >
                       Cerrar Sesión
                     </button>
                   </div>
                 </div>
               )}
             </div>

             {/* Botón hamburger para móvil */}
             <div className="lg:hidden flex items-center space-x-2 sm:space-x-4">
               <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full flex items-center justify-center text-white font-semibold ring-2 ring-slate-500/30 text-xs sm:text-sm">
                 {user?.name?.charAt(0) || user?.email?.charAt(0) || 'A'}
               </div>
               <button
                 ref={hamburgerRef}
                 onClick={toggleMobileMenu}
                 className={`transition-all duration-700 ease-out p-1 sm:p-2 ${
                   theme === 'dark'
                     ? 'text-slate-300 hover:text-white'
                     : 'text-black hover:text-gray-800'
                 }`}
               >
                 <div className="w-4 h-4 sm:w-5 sm:h-5 flex flex-col justify-center items-center">
                   <span className={`block w-3 h-0.5 sm:w-4 sm:h-0.5 bg-current transition-all duration-300 ${isMobileMenuOpen ? 'rotate-45 translate-y-0.5 sm:translate-y-1' : ''}`}></span>
                   <span className={`block w-3 h-0.5 sm:w-4 sm:h-0.5 bg-current transition-all duration-300 mt-0.5 sm:mt-1 ${isMobileMenuOpen ? 'opacity-0' : ''}`}></span>
                   <span className={`block w-3 h-0.5 sm:w-4 sm:h-0.5 bg-current transition-all duration-300 mt-0.5 sm:mt-1 ${isMobileMenuOpen ? '-rotate-45 -translate-y-0.5 sm:-translate-y-1' : ''}`}></span>
                 </div>
               </button>
             </div>
           </div>
         </div>
       </div>

      {/* Menú móvil */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40">
          {/* Overlay */}
          <div 
            className={`absolute inset-0 backdrop-blur-sm transition-all duration-700 ease-out ${
              theme === 'dark' ? 'bg-slate-900/50' : 'bg-blue-900/20'
            }`}
            onClick={toggleMobileMenu}
          />
          
          {/* Menú lateral */}
          <div 
            ref={mobileMenuRef}
            className={`absolute right-0 top-0 h-full w-72 sm:w-80 backdrop-blur-xl border-l shadow-2xl transition-all duration-700 ease-out ${
              theme === 'dark'
                ? 'bg-slate-800/95 border-slate-600/50'
                : 'bg-white/95 border-blue-200/50'
            }`}
          >
            <div className="p-4 sm:p-6">
              {/* Header del menú móvil */}
              <div className="mb-6 sm:mb-8">
                <div className="flex items-center mb-3 sm:mb-4">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full flex items-center justify-center text-white font-semibold mr-3 text-sm sm:text-base">
                    {user?.name?.charAt(0) || user?.email?.charAt(0) || 'A'}
                  </div>
                  <div>
                    <div className={`font-medium transition-all duration-700 ease-out text-sm sm:text-base ${
                      theme === 'dark' ? 'text-white' : 'text-blue-900'
                    }`}>
                      {user?.name || user?.email?.split('@')[0] || 'Admin1'}
                    </div>
                    <div className={`text-xs sm:text-sm transition-all duration-700 ease-out ${
                      theme === 'dark' ? 'text-slate-400' : 'text-blue-600'
                    }`}>
                      {user?.role === 'admin' ? 'Administrador' : 'Usuario'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Navegación móvil */}
              <nav className="space-y-1 sm:space-y-2">
                <a
                  href="/dashboard"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard');
                    setIsMobileMenuOpen(false);
                  }}
                  className={`block px-3 sm:px-4 py-2 sm:py-3 rounded-lg transition-all duration-700 ease-out text-sm sm:text-base ${
                    theme === 'dark'
                      ? 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                      : 'text-black hover:text-gray-800 hover:bg-gray-100/50'
                  } ${location.pathname === '/dashboard' 
                      ? (theme === 'dark' ? 'bg-slate-700/50 text-white' : 'bg-gray-100/50 text-black')
                      : ''
                  }`}
                >
                  Panel
                </a>
                
                {/* Sección de usuarios removida - solo queda el Panel */}
              </nav>

              {/* Separador */}
              <div className={`border-t my-4 sm:my-6 transition-all duration-700 ease-out ${
                theme === 'dark' ? 'border-slate-600/50' : 'border-blue-200/50'
              }`}></div>

              {/* Acciones del usuario */}
              <div className="space-y-1 sm:space-y-2">
                <button
                  onClick={() => {
                    navigate('/profile');
                    setIsMobileMenuOpen(false);
                  }}
                  className={`block w-full text-left px-3 sm:px-4 py-2 sm:py-3 rounded-lg transition-all duration-700 ease-out text-sm sm:text-base ${
                    theme === 'dark'
                      ? 'text-slate-300 hover:text-white hover:bg-gray-800/50'
                      : 'text-black hover:text-gray-800 hover:bg-gray-100/50'
                  } ${location.pathname === '/profile' 
                    ? (theme === 'dark' ? 'bg-slate-700/50 text-white' : 'bg-gray-100/50 text-black')
                    : ''
                  }`}
                >
                  Perfil
                </button>
                <button
                  onClick={() => {
                    navigate('/change-password');
                    setIsMobileMenuOpen(false);
                  }}
                  className={`block w-full text-left px-3 sm:px-4 py-2 sm:py-3 rounded-lg transition-all duration-700 ease-out text-sm sm:text-base ${
                    theme === 'dark'
                      ? 'text-slate-300 hover:text-white hover:bg-gray-800/50'
                      : 'text-black hover:text-gray-800 hover:bg-gray-100/50'
                  } ${location.pathname === '/change-password' 
                    ? (theme === 'dark' ? 'bg-slate-700/50 text-white' : 'bg-gray-100/50 text-black')
                    : ''
                  }`}
                >
                  Configuración
                </button>
                <button
                  onClick={() => {
                    handleLogout();
                    setIsMobileMenuOpen(false);
                  }}
                  className="w-full text-left px-3 sm:px-4 py-2 sm:py-3 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-700 ease-out text-sm sm:text-base"
                >
                  Cerrar Sesión
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main content con padding superior para el navbar fijo */}
      <main className="pt-28 sm:pt-36 px-3 sm:px-4 lg:px-6 pb-4 sm:pb-6 w-full h-full overflow-hidden">
        <Outlet />
      </main>

      {/* Overlay para cerrar dropdowns */}
      {activeDropdown && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setActiveDropdown(null)}
        />
      )}
    </div>
    </AuroraBackground>
  );
};

export default Layout;
