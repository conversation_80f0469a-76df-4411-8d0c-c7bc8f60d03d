/**
 * Manejador de errores HTTP para evitar logs innecesarios en la consola
 */

// Errores HTTP que no queremos que aparezcan en la consola
const SILENT_ERRORS = [
  401, // Unauthorized
  403, // Forbidden
  404, // Not Found
  429, // Too Many Requests
];

/**
 * Maneja errores HTTP de manera silenciosa
 * @param {Response} response - Respuesta HTTP
 * @param {string} defaultMessage - Mensaje por defecto
 * @returns {Promise<{success: boolean, message: string}>}
 */
export const handleHttpError = async (response, defaultMessage = 'Error en la solicitud') => {
  if (response.ok) {
    return { success: true };
  }

  // Si es un error que queremos silenciar, no hacer console.log
  if (SILENT_ERRORS.includes(response.status)) {
    let message = defaultMessage;
    
    try {
      const errorData = await response.json();
      // Filtrar mensajes que contengan contadores de intentos
      if (errorData.message && !errorData.message.includes('998') && !errorData.message.includes('intentos')) {
        message = errorData.message;
      }
    } catch {
      // Si no se puede parsear, usar mensaje por defecto
    }
    
    return { success: false, message };
  }

  // Para otros errores, permitir logs normales
  console.error(`HTTP Error ${response.status}: ${response.statusText}`);
  return { success: false, message: defaultMessage };
};

/**
 * Wrapper para fetch que maneja errores automáticamente
 * @param {string} url - URL a consultar
 * @param {Object} options - Opciones de fetch
 * @param {string} defaultErrorMessage - Mensaje de error por defecto
 * @returns {Promise<Response>}
 */
export const silentFetch = async (url, options = {}, defaultErrorMessage = 'Error en la solicitud') => {
  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorResult = await handleHttpError(response, defaultErrorMessage);
    throw new Error(errorResult.message);
  }
  
  return response;
};
